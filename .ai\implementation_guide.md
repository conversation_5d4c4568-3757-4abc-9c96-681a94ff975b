# Joseph-AI + Mem0 Implementation Guide

## Prerequisites & Environment Setup

### System Requirements
```bash
# Required Software
- Node.js 18+ (for compatibility)
- Bun (latest version)
- Git
- Ollama (for local AI models)
- Python 3.8+ (for Mem0)
- SQLite 3

# Optional
- Docker (for containerization)
- VS Code (recommended IDE)
```

### Installation Steps

#### 1. Install Core Dependencies
```bash
# Install Bun (if not already installed)
curl -fsSL https://bun.sh/install | bash

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve

# Pull a model (recommended: llama2 or codellama)
ollama pull llama2:7b
ollama pull codellama:7b
```

#### 2. Project Initialization
```bash
# Create new Next.js project with Bun
bunx create-next-app@latest joseph-ai-enhanced --typescript --tailwind --eslint --app
cd joseph-ai-enhanced

# Initialize project structure
mkdir -p {lib,components,hooks,types,services,utils,stores,data}
mkdir -p {lib/{ai,memory,rag,storage},services/{ollama,mem0,vectordb}}
mkdir -p {components/{chat,memory,documents,settings},hooks/{use-ai,use-memory,use-rag}}

# Install core dependencies
bun add @mem0ai/mem0 @langchain/core @langchain/community
bun add @supabase/supabase-js sqlite3 better-sqlite3
bun add zustand @tanstack/react-query axios
bun add @headlessui/react @heroicons/react
bun add react-markdown react-syntax-highlighter
bun add @types/better-sqlite3 -d

# Install development dependencies
bun add -d @types/node @types/react @types/react-dom
bun add -d vitest @vitejs/plugin-react jsdom
bun add -d playwright @playwright/test
```

## Core Architecture Implementation

### 1. Database Schema Setup

```typescript
// lib/storage/database.ts
import Database from 'better-sqlite3';
import { join } from 'path';

export interface ConversationRecord {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  metadata?: string;
}

export interface MessageRecord {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: string;
}

export interface DocumentRecord {
  id: string;
  filename: string;
  content: string;
  embedding?: string;
  metadata?: string;
  created_at: string;
}

class DatabaseManager {
  private db: Database.Database;

  constructor(dbPath?: string) {
    const path = dbPath || join(process.cwd(), 'data', 'joseph-ai.db');
    this.db = new Database(path);
    this.initializeTables();
  }

  private initializeTables() {
    // Conversations table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        metadata TEXT
      )
    `);

    // Messages table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        metadata TEXT,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id)
      )
    `);

    // Documents table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        content TEXT NOT NULL,
        embedding TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL
      )
    `);

    // Memory entries table (for Mem0 integration)
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS memory_entries (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        content TEXT NOT NULL,
        category TEXT,
        importance_score REAL,
        created_at TEXT NOT NULL,
        accessed_at TEXT,
        metadata TEXT
      )
    `);
  }

  // Database operations
  getConversations() {
    return this.db.prepare('SELECT * FROM conversations ORDER BY updated_at DESC').all();
  }

  createConversation(conversation: Omit<ConversationRecord, 'id'>) {
    const id = crypto.randomUUID();
    const stmt = this.db.prepare(`
      INSERT INTO conversations (id, title, created_at, updated_at, metadata)
      VALUES (?, ?, ?, ?, ?)
    `);
    stmt.run(id, conversation.title, conversation.created_at, conversation.updated_at, conversation.metadata);
    return id;
  }

  getMessages(conversationId: string) {
    return this.db.prepare('SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC').all(conversationId);
  }

  addMessage(message: Omit<MessageRecord, 'id'>) {
    const id = crypto.randomUUID();
    const stmt = this.db.prepare(`
      INSERT INTO messages (id, conversation_id, role, content, timestamp, metadata)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    stmt.run(id, message.conversation_id, message.role, message.content, message.timestamp, message.metadata);
    return id;
  }

  close() {
    this.db.close();
  }
}

export const db = new DatabaseManager();
```

### 2. Mem0 Integration Service

```typescript
// services/mem0/mem0-service.ts
import { Memory } from '@mem0ai/mem0';

export interface MemoryEntry {
  id: string;
  content: string;
  category?: string;
  importance?: number;
  metadata?: Record<string, any>;
  timestamp: string;
}

export class Mem0Service {
  private memory: Memory;
  private userId: string;

  constructor(userId: string = 'default') {
    this.userId = userId;
    this.memory = new Memory({
      // Configure for local usage
      vector_store: {
        provider: 'chroma',
        config: {
          path: './data/chroma_db'
        }
      },
      embedder: {
        provider: 'ollama',
        config: {
          model: 'nomic-embed-text'
        }
      }
    });
  }

  async addMemory(content: string, metadata?: Record<string, any>): Promise<string> {
    try {
      const result = await this.memory.add(content, {
        user_id: this.userId,
        metadata
      });
      return result.id;
    } catch (error) {
      console.error('Error adding memory:', error);
      throw error;
    }
  }

  async searchMemories(query: string, limit: number = 5): Promise<MemoryEntry[]> {
    try {
      const results = await this.memory.search(query, {
        user_id: this.userId,
        limit
      });
      
      return results.map(result => ({
        id: result.id,
        content: result.text,
        category: result.metadata?.category,
        importance: result.score,
        metadata: result.metadata,
        timestamp: result.created_at || new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error searching memories:', error);
      return [];
    }
  }

  async getAllMemories(): Promise<MemoryEntry[]> {
    try {
      const results = await this.memory.get_all({
        user_id: this.userId
      });
      
      return results.map(result => ({
        id: result.id,
        content: result.text,
        category: result.metadata?.category,
        importance: result.score,
        metadata: result.metadata,
        timestamp: result.created_at || new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error getting all memories:', error);
      return [];
    }
  }

  async updateMemory(id: string, content: string, metadata?: Record<string, any>): Promise<void> {
    try {
      await this.memory.update(id, {
        text: content,
        metadata
      });
    } catch (error) {
      console.error('Error updating memory:', error);
      throw error;
    }
  }

  async deleteMemory(id: string): Promise<void> {
    try {
      await this.memory.delete(id);
    } catch (error) {
      console.error('Error deleting memory:', error);
      throw error;
    }
  }
}
```

### 3. Ollama Integration Service

```typescript
// services/ollama/ollama-service.ts
export interface OllamaModel {
  name: string;
  size: string;
  digest: string;
  modified_at: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatResponse {
  message: ChatMessage;
  done: boolean;
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export class OllamaService {
  private baseUrl: string;
  private currentModel: string;

  constructor(baseUrl: string = 'http://localhost:11434', defaultModel: string = 'llama2:7b') {
    this.baseUrl = baseUrl;
    this.currentModel = defaultModel;
  }

  async listModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('Error listing models:', error);
      return [];
    }
  }

  async pullModel(model: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: model })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to pull model: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error pulling model:', error);
      throw error;
    }
  }

  async chat(messages: ChatMessage[], model?: string): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: model || this.currentModel,
          messages,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`Chat request failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error in chat:', error);
      throw error;
    }
  }

  async *chatStream(messages: ChatMessage[], model?: string): AsyncGenerator<Partial<ChatResponse>, void, unknown> {
    try {
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: model || this.currentModel,
          messages,
          stream: true
        })
      });

      if (!response.ok || !response.body) {
        throw new Error(`Stream request failed: ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            yield data;
          } catch (e) {
            // Skip invalid JSON lines
          }
        }
      }
    } catch (error) {
      console.error('Error in chat stream:', error);
      throw error;
    }
  }

  setModel(model: string) {
    this.currentModel = model;
  }

  getCurrentModel(): string {
    return this.currentModel;
  }
}
```

### 4. AI Service Layer (Unified Interface)

```typescript
// lib/ai/ai-service.ts
import { OllamaService, ChatMessage } from '@/services/ollama/ollama-service';
import { Mem0Service } from '@/services/mem0/mem0-service';

export interface AIResponse {
  content: string;
  model: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface AIConfig {
  provider: 'ollama' | 'openai' | 'anthropic';
  model: string;
  useMemory: boolean;
  temperature?: number;
  maxTokens?: number;
}

export class AIService {
  private ollamaService: OllamaService;
  private memoryService: Mem0Service;
  private config: AIConfig;

  constructor(userId: string = 'default', config?: Partial<AIConfig>) {
    this.ollamaService = new OllamaService();
    this.memoryService = new Mem0Service(userId);
    this.config = {
      provider: 'ollama',
      model: 'llama2:7b',
      useMemory: true,
      temperature: 0.7,
      maxTokens: 2000,
      ...config
    };
  }

  async chat(message: string, conversationHistory: ChatMessage[] = []): Promise<AIResponse> {
    try {
      // Enhance context with memory if enabled
      let enhancedMessages = [...conversationHistory];
      
      if (this.config.useMemory) {
        const relevantMemories = await this.memoryService.searchMemories(message, 3);
        if (relevantMemories.length > 0) {
          const memoryContext = relevantMemories
            .map(memory => memory.content)
            .join('\n');
          
          enhancedMessages.unshift({
            role: 'system',
            content: `Relevant context from previous conversations:\n${memoryContext}\n\nUse this context to provide more personalized and informed responses.`
          });
        }
      }

      // Add current user message
      enhancedMessages.push({ role: 'user', content: message });

      // Get AI response based on provider
      let response: AIResponse;
      
      switch (this.config.provider) {
        case 'ollama':
          const ollamaResponse = await this.ollamaService.chat(enhancedMessages, this.config.model);
          response = {
            content: ollamaResponse.message.content,
            model: this.config.model,
            timestamp: new Date().toISOString(),
            metadata: {
              provider: 'ollama',
              duration: ollamaResponse.total_duration,
              eval_count: ollamaResponse.eval_count
            }
          };
          break;
        
        default:
          throw new Error(`Provider ${this.config.provider} not implemented`);
      }

      // Store interaction in memory if enabled
      if (this.config.useMemory) {
        await this.memoryService.addMemory(
          `User: ${message}\nAssistant: ${response.content}`,
          {
            type: 'conversation',
            model: this.config.model,
            timestamp: response.timestamp
          }
        );
      }

      return response;
    } catch (error) {
      console.error('Error in AI chat:', error);
      throw error;
    }
  }

  async *chatStream(message: string, conversationHistory: ChatMessage[] = []): AsyncGenerator<Partial<AIResponse>, void, unknown> {
    try {
      // Similar memory enhancement as in chat method
      let enhancedMessages = [...conversationHistory];
      
      if (this.config.useMemory) {
        const relevantMemories = await this.memoryService.searchMemories(message, 3);
        if (relevantMemories.length > 0) {
          const memoryContext = relevantMemories
            .map(memory => memory.content)
            .join('\n');
          
          enhancedMessages.unshift({
            role: 'system',
            content: `Relevant context: ${memoryContext}`
          });
        }
      }

      enhancedMessages.push({ role: 'user', content: message });

      // Stream response
      let fullContent = '';
      
      for await (const chunk of this.ollamaService.chatStream(enhancedMessages, this.config.model)) {
        if (chunk.message?.content) {
          fullContent += chunk.message.content;
          yield {
            content: chunk.message.content,
            model: this.config.model,
            timestamp: new Date().toISOString()
          };
        }
      }

      // Store complete interaction in memory
      if (this.config.useMemory && fullContent) {
        await this.memoryService.addMemory(
          `User: ${message}\nAssistant: ${fullContent}`,
          {
            type: 'conversation',
            model: this.config.model,
            timestamp: new Date().toISOString()
          }
        );
      }
    } catch (error) {
      console.error('Error in AI chat stream:', error);
      throw error;
    }
  }

  updateConfig(config: Partial<AIConfig>) {
    this.config = { ...this.config, ...config };
    if (config.model && this.config.provider === 'ollama') {
      this.ollamaService.setModel(config.model);
    }
  }

  getConfig(): AIConfig {
    return { ...this.config };
  }
}
```

### 5. React Components

#### Chat Interface Component
```typescript
// components/chat/chat-interface.tsx
'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2 } from 'lucide-react';
import { AIService } from '@/lib/ai/ai-service';
import { ChatMessage } from '@/services/ollama/ollama-service';

interface Message extends ChatMessage {
  id: string;
  timestamp: string;
}

export function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aiService] = useState(() => new AIService());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: crypto.randomUUID(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const conversationHistory = messages.map(({ id, timestamp, ...msg }) => msg);
      
      // Create assistant message placeholder
      const assistantMessageId = crypto.randomUUID();
      const assistantMessage: Message = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString()
      };
      
      setMessages(prev => [...prev, assistantMessage]);

      // Stream response
      let fullContent = '';
      for await (const chunk of aiService.chatStream(userMessage.content, conversationHistory)) {
        if (chunk.content) {
          fullContent += chunk.content;
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: fullContent }
                : msg
            )
          );
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
      setMessages(prev => [...prev, {
        id: crypto.randomUUID(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <Bot className="mx-auto mb-4 w-12 h-12" />
            <h3 className="text-lg font-semibold">Welcome to Joseph-AI Enhanced</h3>
            <p>Start a conversation to experience AI with memory and context.</p>
          </div>
        )}
        
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex max-w-[80%] ${
                message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                message.role === 'user' 
                  ? 'bg-blue-500 text-white ml-3' 
                  : 'bg-gray-200 text-gray-600 mr-3'
              }`}>
                {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
              </div>
              
              <div className={`rounded-lg px-4 py-2 ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="prose prose-sm max-w-none">
                  {message.content}
                </div>
                <div className="text-xs opacity-70 mt-1">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-4 py-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm text-gray-600">Thinking...</span>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Form */}
      <div className="border-t bg-white p-4">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="bg-blue-500 text-white rounded-lg px-4 py-2 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  );
}
```

#### Memory Dashboard Component
```typescript
// components/memory/memory-dashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Brain, Search, Trash2, Edit, Plus } from 'lucide-react';
import { Mem0Service, MemoryEntry } from '@/services/mem0/mem0-service';

export function MemoryDashboard() {
  const [memories, setMemories] = useState<MemoryEntry[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [memoryService] = useState(() => new Mem0Service());
  const [editingMemory, setEditingMemory] = useState<MemoryEntry | null>(null);

  useEffect(() => {
    loadMemories();
  }, []);

  const loadMemories = async () => {
    try {
      setIsLoading(true);
      const allMemories = await memoryService.getAllMemories();
      setMemories(allMemories);
    } catch (error) {
      console.error('Error loading memories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadMemories();
      return;
    }

    try {
      setIsLoading(true);
      const searchResults = await memoryService.searchMemories(searchQuery, 20);
      setMemories(searchResults);
    } catch (error) {
      console.error('Error searching memories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteMemory = async (id: string) => {
    if (!confirm('Are you sure you want to delete this memory?')) return;

    try {
      await memoryService.deleteMemory(id);
      setMemories(prev => prev.filter(memory => memory.id !== id));
    } catch (error) {
      console.error('Error deleting memory:', error);
    }
  };

  const handleEditMemory = async (memory: MemoryEntry, newContent: string) => {
    try {
      await memoryService.updateMemory(memory.id, newContent, memory.metadata);
      setMemories(prev => 
        prev.map(m => 
          m.id === memory.id 
            ? { ...m, content: newContent }
            : m
        )
      );
      setEditingMemory(null);
    } catch (error) {
      console.error('Error updating memory:', error);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <Brain className="w-8 h-8 text-purple-600" />
          <h1 className="text-3xl font-bold text-gray-900">Memory Dashboard</h1>
        </div>
        
        {/* Search */}
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Search memories..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          <button
            onClick={handleSearch}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Search
          </button>
          <button
            onClick={loadMemories}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Show All
          </button>
        </div>
      </div>

      {/* Memories Grid */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading memories...</p>
        </div>
      ) : memories.length === 0 ? (
        <div className="text-center py-8">
          <Brain className="mx-auto w-16 h-16 text-gray-300 mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No memories found</h3>
          <p className="text-gray-500">Start chatting to build your AI's memory!</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {memories.map((memory) => (
            <div key={memory.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center space-x-2">
                  {memory.category && (
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                      {memory.category}
                    </span>
                  )}
                  {memory.importance && (
                    <span className="text-xs text-gray-500">
                      Score: {memory.importance.toFixed(2)}
                    </span>
                  )}
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => setEditingMemory(memory)}
                    className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteMemory(memory.id)}
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="text-sm text-gray-800 mb-3 line-clamp-4">
                {memory.content}
              </div>
              
              <div className="text-xs text-gray-500">
                {new Date(memory.timestamp).toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Edit Memory Modal */}
      {editingMemory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full p-6">
            <h3 className="text-lg font-semibold mb-4">Edit Memory</h3>
            <textarea
              defaultValue={editingMemory.content}
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Memory content..."
              id="edit-memory-content"
            />
            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setEditingMemory(null)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const textarea = document.getElementById('edit-memory-content') as HTMLTextAreaElement;
                  handleEditMemory(editingMemory, textarea.value);
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

### 6. Next.js API Routes

```typescript
// app/api/chat/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/lib/ai/ai-service';

export async function POST(request: NextRequest) {
  try {
    const { message, conversationHistory, config } = await request.json();
    
    const aiService = new AIService('default', config);
    const response = await aiService.chat(message, conversationHistory);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

```typescript
// app/api/memory/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Mem0Service } from '@/services/mem0/mem0-service';

const memoryService = new Mem0Service();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    
    if (query) {
      const memories = await memoryService.searchMemories(query, 20);
      return NextResponse.json(memories);
    } else {
      const memories = await memoryService.getAllMemories();
      return NextResponse.json(memories);
    }
  } catch (error) {
    console.error('Memory GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { content, metadata } = await request.json();
    const id = await memoryService.addMemory(content, metadata);
    return NextResponse.json({ id });
  } catch (error) {
    console.error('Memory POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, content, metadata } = await request.json();
    await memoryService.updateMemory(id, content, metadata);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Memory PUT error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Memory ID required' },
        { status: 400 }
      );
    }
    
    await memoryService.deleteMemory(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Memory DELETE error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 7. Main Application Layout

```typescript
// app/layout.tsx
import './globals.css';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Joseph-AI Enhanced',
  description: 'AI Assistant with Memory and Local Processing',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </body>
    </html>
  );
}
```

```typescript
// app/page.tsx
'use client';

import { useState } from 'react';
import { ChatInterface } from '@/components/chat/chat-interface';
import { MemoryDashboard } from '@/components/memory/memory-dashboard';
import { MessageSquare, Brain, Settings, Menu, X } from 'lucide-react';

export default function HomePage() {
  const [activeTab, setActiveTab] = useState<'chat' | 'memory' | 'settings'>('chat');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const tabs = [
    { id: 'chat' as const, label: 'Chat', icon: MessageSquare },
    { id: 'memory' as const, label: 'Memory', icon: Brain },
    { id: 'settings' as const, label: 'Settings', icon: Settings },
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-4 border-b">
          <h1 className="text-xl font-bold text-gray-900">Joseph-AI Enhanced</h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <nav className="mt-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 transition-colors ${
                  activeTab === tab.id ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between h-16 px-4 bg-white border-b lg:px-6">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <Menu className="w-5 h-5" />
          </button>
          
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-900 capitalize">
              {activeTab}
            </h2>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'chat' && <ChatInterface />}
          {activeTab === 'memory' && <MemoryDashboard />}
          {activeTab === 'settings' && <div className="p-6">Settings coming soon...</div>}
        </div>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
```

## Production Deployment & Development Workflow

### 1. Development Scripts

```json
// package.json scripts section
{
  "scripts": {
    "dev": "next dev --turbo",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:e2e": "playwright test",
    "setup": "bun run setup:ollama && bun run setup:mem0",
    "setup:ollama": "ollama pull llama2:7b && ollama pull nomic-embed-text",
    "setup:mem0": "python -m pip install mem0ai",
    "clean": "rm -rf .next node_modules/.cache",
    "dev:local": "bun run setup && bun run dev"
  }
}
```

### 2. Environment Configuration

```bash
# .env.local
NEXT_PUBLIC_APP_NAME=Joseph-AI Enhanced
NEXT_PUBLIC_OLLAMA_BASE_URL=http://localhost:11434
DATABASE_URL=file:./data/joseph-ai.db
CHROMA_DB_PATH=./data/chroma_db

# Optional cloud services
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
PINECONE_API_KEY=your_pinecone_key_here
```

### 3. Docker Setup (Optional)

```dockerfile
# Dockerfile
FROM oven/bun:1 as builder

WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

COPY . .
RUN bun run build

FROM oven/bun:1 as runner
WORKDIR /app

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
CMD ["bun", "run", "server.js"]
```

### 4. Setup Instructions

```bash
# Complete setup script
#!/bin/bash

echo "🚀 Setting up Joseph-AI Enhanced..."

# Install dependencies
echo "📦 Installing dependencies..."
bun install

# Setup directories
echo "📁 Creating directories..."
mkdir -p data
mkdir -p data/chroma_db
mkdir -p data/uploads

# Install Python dependencies for Mem0
echo "🐍 Setting up Mem0..."
python -m pip install mem0ai

# Setup Ollama models
echo "🤖 Setting up Ollama..."
ollama pull llama2:7b
ollama pull nomic-embed-text

# Initialize database
echo "🗄️ Initializing database..."
bun run setup:db

# Build application
echo "🔨 Building application..."
bun run build

echo "✅ Setup complete! Run 'bun run dev' to start development."
```

### 5. Testing Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
  },
  resolve: {
    alias: {
      '@': new URL('./', import.meta.url).pathname,
    },
  },
});
```

### 6. Deployment Configuration

```typescript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['better-sqlite3'],
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
      };
    }
    return config;
  },
  output: 'standalone',
};

module.exports = nextConfig;
```

## Usage Instructions

### Quick Start
1. **Clone and Setup:** `git clone <repo> && cd joseph-ai-enhanced && bun install`
2. **Environment:** Copy `.env.example` to `.env.local` and configure
3. **Dependencies:** Run `bun run setup` to install Ollama models and Mem0
4. **Development:** Run `bun run dev` to start development server
5. **Access:** Open `http://localhost:3000` in your browser

### Key Features
- **Memory-Aware Chat:** Conversations automatically build contextual memory
- **Local AI Models:** Full offline functionality with Ollama
- **Memory Management:** Visual dashboard to explore and manage AI memories
- **Extensible Architecture:** Easy to add new AI providers and features
- **Production Ready:** Deployable to Vercel with local development support

### Extending the Application
- **Add New AI Providers:** Extend `AIService` class with new provider implementations
- **Custom Memory Types:** Extend `Mem0Service` with specialized memory categories
- **RAG Integration:** Add document processing and vector search capabilities
- **Plugin System:** Create modular plugins for specific use cases

This implementation provides a solid foundation for combining Joseph-AI with Mem0 while maintaining extensibility and production readiness.