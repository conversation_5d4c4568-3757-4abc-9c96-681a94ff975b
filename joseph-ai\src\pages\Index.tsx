
import { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import Sidebar from '@/components/Sidebar';
import ChatHeader from '@/components/ChatHeader';
import ChatInput from '@/components/ChatInput';
import ActionButtons from '@/components/ActionButtons';
import MessageList from '@/components/MessageList';
import { Badge } from '@/components/ui/badge';
import { setApiKey, setModel } from '@/api/aiClient';
import { toast } from "@/components/ui/use-toast";

const Index = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { messages, isLoading, sendMessage, connectionStatus, currentModel, setCurrentModel } = useChat();

  const handleApiKeyChange = (apiKey: string) => {
    setApiKey(apiKey);
    // Try to reconnect after setting API key
    toast({
      title: "API Key Updated",
      description: "Your API key has been updated and will be used for future requests.",
    });
  };

  const handleModelChange = (model: string) => {
    setModel(model);
    setCurrentModel(model);
    toast({
      title: "Model Changed",
      description: `Now using model: ${model}`,
    });
  };

  return (
    <div className="flex h-screen">
      <Sidebar 
        isOpen={isSidebarOpen} 
        onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        onApiKeyChange={handleApiKeyChange}
        onModelChange={handleModelChange}
      />
      
      <main className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'ml-64' : 'ml-0'}`}>
        <ChatHeader isSidebarOpen={isSidebarOpen} />
        
        <div className={`flex h-full flex-col ${messages.length === 0 ? 'items-center justify-center' : 'justify-between'} pt-[60px] pb-4`}>
          {messages.length === 0 ? (
            <div className="w-full max-w-3xl px-4 space-y-4">
              <div className="text-center mb-4">
                <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'} className="mb-4">
                  {connectionStatus === 'connected' ? '✓ LM Studio/Ollama Connected' : '✗ LM Studio/Ollama Not Connected'}
                </Badge>
                {connectionStatus !== 'connected' && (
                  <p className="text-sm text-yellow-600 mt-2">
                    Ensure your local LM Studio/Ollama is running on port 1234
                  </p>
                )}
                {currentModel && connectionStatus === 'connected' && (
                  <p className="text-sm text-green-600 mt-2">
                    Using model: {currentModel}
                  </p>
                )}
              </div>
              <div>
                <h1 className="mb-8 text-4xl font-semibold text-center">What can I help with?</h1>
                <ChatInput onSend={sendMessage} isLoading={isLoading} />
              </div>
              <ActionButtons />
            </div>
          ) : (
            <>
              <MessageList messages={messages} />
              <div className="w-full max-w-3xl mx-auto px-4 py-2">
                <div className="flex items-center justify-center mb-2">
                  <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'} className="mb-1">
                    {connectionStatus === 'connected' ? `✓ Connected (${currentModel})` : '✗ Not Connected'}
                  </Badge>
                </div>
                <ChatInput onSend={sendMessage} isLoading={isLoading} />
              </div>
            </>
          )}
        </div>
      </main>
    </div>
  );
};

export default Index;
