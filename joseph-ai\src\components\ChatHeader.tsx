
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "lucide-react";

type ChatHeaderProps = {
  isSidebarOpen: boolean;
};

const ChatHeader = ({ isSidebarOpen }: ChatHeaderProps) => {
  return (
    <header className="fixed top-0 left-0 right-0 z-10 bg-background/80 backdrop-blur-sm border-b">
      <div className={`h-14 flex items-center justify-between px-4 transition-all duration-300 ${isSidebarOpen ? 'ml-64' : 'ml-0'}`}>
        <div className="flex items-center gap-3">
          <img 
            src="/lovable-uploads/2768450d-71d8-4559-9b83-847a1ce5e000.png"
            alt="Joseph AI Logo"
            className="w-8 h-8"
          />
          <h1 className="text-xl font-bold">Joseph AI</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon">
            <Brain className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default ChatHeader;
