
# Joseph AI - Project Description

## Overview
Joseph AI is a full-stack web application designed to interact with local AI models through LM Studio/Ollama endpoints. The application serves as a personal assistant for <PERSON>, helping manage coding workflows, project planning, technical documentation, and custom prompts.

## Core Features (MVP)
- **Modern Frontend**: Built with React, TypeScript, and Tailwind CSS
- **Local AI Integration**: Connects to local LM Studio/Ollama instance at `http://localhost:1234`
- **Chat Interface**: Interact with AI models using `/v1/chat/completions` endpoint
- **Configurable**: System prompt and custom instructions from a config file
- **Message History**: Basic storage using browser localStorage or in-memory state
- **Responsive Design**: Optimized for both mobile and desktop use
- **Auto-reconnect**: Reconnects if the local AI server restarts
- **Status Indicator**: Shows connection status with the local AI backend

## API Routes
- `/api/chat`: 
  - POST route
  - Accepts `{ messages: [], systemPrompt: string }`
  - Forwards requests to `http://localhost:1234/v1/chat/completions`
  - Streams the response back to the client
- `/api/models` (Optional):
  - GET route
  - Lists available models via `http://localhost:1234/v1/models`

## Architecture
- **Frontend**: TypeScript, Tailwind CSS, React
- **State Management**: React Context API or local state hooks
- **Backend Connectivity**: Fetch API or Axios for HTTP requests
- **Configuration**: `joseph-config.ts` file for system settings
  - Model name (default: `deepseek-coder:7b`)
  - AI personality settings
  - Memory toggle
  - Other configuration options

## Deployment & Integration Options
1. **Vercel Deployment**:
   - Setup for deployment to Vercel while maintaining connection to local AI
   - Configuration for environment variables

2. **Database Integration**:
   - Support for local MySQL or PostgreSQL databases
   - Connection utilities and schema setup

3. **Server Integration**:
   - Compatible with cPanel hosting
   - Adaptable to Cloud/VPS server environments
   - Instructions for deployment to various hosting solutions

4. **Supabase Integration** (Optional):
   - Alternative backend solution using Supabase
   - Authentication and database setup

## Future Features (Development Roadmap)
- **Memory System**: Vector storage and embeddings for context awareness
- **File Operations**: Upload and process files for AI analysis
- **Command Execution**: Execute commands based on AI recommendations
- **Multi-agent Support**: Coordinate multiple AI agents for complex tasks

## Development Instructions
1. **Setup**:
   ```bash
   git clone [repository]
   npm install
   npm run dev
   ```

2. **Local Development**:
   - Ensure LM Studio/Ollama is running on port 1234
   - Use default model `deepseek-coder:7b` or configure in settings

3. **Project Structure**:
   - `/src/components`: UI components
   - `/src/contexts`: Context providers for state management
   - `/src/api`: API client functions
   - `/src/config`: Configuration files
   - `/src/utils`: Utility functions
   - `/src/types`: TypeScript type definitions

4. **Git Integration**:
   ```bash
   git init
   git remote add origin https://github.com/YOUR_USERNAME/joseph-ai.git
   git add .
   git commit -m "Initial Lovable AI project"
   git push -u origin master
   ```

5. **Build Process**:
   ```bash
   npm run build
   ```
   - Target build size: Under 10MB

## Technical Requirements
- **Browser Storage**: LocalStorage for chat history and settings
- **API Handling**: Streaming responses from AI model
- **Error Handling**: Graceful degradation if local AI service is unavailable
- **Performance**: Optimized for quick responses and minimal resource usage

This project is designed to be lightweight, efficient, and easily extensible for future features and integrations. The core focus is on providing a seamless interface to interact with local AI models while maintaining flexibility for various deployment scenarios.
