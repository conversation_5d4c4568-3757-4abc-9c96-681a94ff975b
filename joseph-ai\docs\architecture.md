
# Joseph AI Architecture

This document describes the architectural design and component flow of the Joseph AI application.

## Overview

Joseph AI is a React-based web application that connects to local large language models (LLMs) through services like LM Studio or Ollama. It provides a chat interface for interacting with these models and offers features like message history, customizable system prompts, and automatic reconnection.

## Folder Structure

```
src/
├── api/             # API client functions for LLM communication
├── components/      # Reusable UI components
├── config/          # Application configuration
├── contexts/        # React context providers for state management
├── hooks/           # Custom React hooks for shared logic
├── lib/             # Utility libraries and helper functions
├── pages/           # Page components that represent routes
├── types/           # TypeScript type definitions
└── utils/           # Utility functions for core operations
```

## Core Components

### State Management

State management is handled through React Context and custom hooks:

- `ChatContext.tsx`: Main provider for chat functionality
- `useMessageStore.ts`: Manages message history and operations
- `useSystemPrompt.ts`: Handles system prompt configuration
- `useConnectionStatus.ts`: Monitors connection to the LLM server

### LLM Communication Layer

The core AI communication is handled through:

- `utils/aiConnection.ts`: Class for managing connection to LLM server
- `api/aiClient.ts`: Functions for sending/receiving messages

### UI Components

The user interface is composed of:

- `MessageList.tsx`: Displays chat history
- `Message.tsx`: Individual message component
- `ChatInput.tsx`: Input for sending messages
- `ChatHeader.tsx`: Header with logo and connection status
- `Sidebar.tsx`: Sidebar for additional functionality

## Data Flow

1. User enters a message in `ChatInput`
2. Message is sent to `ChatContext.sendMessage()`
3. `ChatContext` adds the message to history using `useMessageStore`
4. The message is sent to the LLM via `api/aiClient.sendMessageStream()`
5. Stream responses are processed and added to the message history
6. `MessageList` updates to display the new messages

## Configuration

The application is configured through:

- `config/joseph-config.ts`: Main configuration file
- Environment variables (loaded from .env files)

## Connection Management

The application manages connection to the LLM server through:

1. Initial connection check on application load
2. Automatic reconnection attempts if configured
3. Status updates displayed in the UI
4. Connection status management via `useConnectionStatus` hook

## Styling

The application uses:

- Tailwind CSS for styling
- Shadcn UI components for common UI elements

## Future Extensions

The architecture is designed to be extensible with placeholders for:

- File upload functionality
- Memory/vector storage
- Multi-agent support
- Command execution
