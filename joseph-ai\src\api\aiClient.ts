
import { aiConnection, type AIConnectionStatus } from "@/utils/aiConnection";
import { josephConfig } from "@/config/joseph-config";
import { ApiMessage } from "@/contexts/ChatContext";

/**
 * Parse the SSE stream from the AI model
 * @param reader ReadableStreamDefaultReader to read from
 * @param onChunk Callback for each chunk of text
 * @param onDone Callback when stream is complete
 */
async function parseSSEResponse(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  onChunk: (text: string, done: boolean) => void,
  onDone: () => void,
  onError: (error: Error) => void
): Promise<void> {
  const decoder = new TextDecoder();
  let buffer = "";
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        if (buffer.length > 0) {
          onChunk(buffer, true);
        }
        onDone();
        return;
      }
      
      buffer += decoder.decode(value, { stream: true });
      
      // Process complete lines
      const lines = buffer.split('\n');
      buffer = lines.pop() || ""; // Keep the incomplete line in the buffer
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          // Check for [DONE] message
          if (data.trim() === '[DONE]') {
            onChunk("", true);
            continue;
          }
          
          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content || "";
            if (content) {
              onChunk(content, false);
            }
          } catch (e) {
            console.error('Error parsing SSE data:', e);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error reading stream:', error);
    onError(error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Set the API key for authentication
 * @param apiKey API key to use for authentication
 */
export function setApiKey(apiKey: string): void {
  aiConnection.setApiKey(apiKey);
}

/**
 * Set the model to use for chat completions
 * @param model Model ID to use
 */
export function setModel(model: string): void {
  aiConnection.setModel(model);
}

/**
 * Get the current selected model
 * @returns Current model ID
 */
export function getCurrentModel(): string {
  return aiConnection.getModel();
}

/**
 * Send a message to the AI model and stream the response
 * @param messages Array of messages to send
 * @param onChunk Callback for each chunk of text
 * @param onComplete Callback when stream is complete
 */
export async function sendMessageStream(
  messages: ApiMessage[],
  onChunk: (text: string, done: boolean) => void,
  onComplete: () => void = () => {},
  onError: (error: Error) => void = () => {}
): Promise<void> {
  try {
    // First check connection
    const connected = await aiConnection.checkConnection();
    if (!connected) {
      throw new Error("Cannot connect to LM Studio/Ollama");
    }
    
    // Send the request
    const stream = await aiConnection.sendChatCompletion(messages);
    
    if (!stream) {
      throw new Error("Failed to get response stream");
    }
    
    // Parse the stream
    const reader = stream.getReader();
    await parseSSEResponse(reader, onChunk, onComplete, onError);
  } catch (error) {
    onError(error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Get available AI models
 * @returns Promise with array of model IDs
 */
export async function getAvailableModels(): Promise<string[]> {
  return aiConnection.getAvailableModels();
}

/**
 * Get the connection status of the AI backend
 * @returns Current connection status
 */
export function getConnectionStatus(): AIConnectionStatus {
  return aiConnection.getStatus();
}

/**
 * Subscribe to connection status changes
 * @param callback Function to call when status changes
 * @returns Unsubscribe function
 */
export function onConnectionStatusChange(callback: (status: AIConnectionStatus) => void) {
  return aiConnection.onStatusChange(callback);
}
