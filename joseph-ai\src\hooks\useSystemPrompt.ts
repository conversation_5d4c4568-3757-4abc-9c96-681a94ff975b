
import { useState, useEffect } from 'react';
import { josephConfig } from '@/config/joseph-config';

export function useSystemPrompt(initialPrompt = josephConfig.systemPrompt.default) {
  const [systemPrompt, setSystemPrompt] = useState(initialPrompt);
  
  // Load system prompt from localStorage if enabled
  useEffect(() => {
    if (josephConfig.app.storageType === 'localStorage') {
      const savedSystemPrompt = localStorage.getItem('joseph-ai-system-prompt');
      
      if (savedSystemPrompt) {
        setSystemPrompt(savedSystemPrompt);
      }
    }
  }, []);

  // Save system prompt to localStorage when it changes
  useEffect(() => {
    if (josephConfig.app.storageType === 'localStorage') {
      localStorage.setItem('joseph-ai-system-prompt', systemPrompt);
    }
  }, [systemPrompt]);

  return {
    systemPrompt,
    setSystemPrompt
  };
}
