---
title: '🔮 Poe Bot'
---

### 🚀 Getting started

1. Install embedchain python package:

```bash
pip install fastapi-poe==0.0.16 
```

2. Create a free account on [<PERSON>](https://www.poe.com?utm_source=embedchain).
3. Click "Create Bot" button on top left.
4. Give it a handle and an optional description.
5. Select `Use API`.
6. Under `API URL` enter your server or ngrok address. You can use your machine's public IP or DNS. Otherwise, employ a proxy server like [ngrok](https://ngrok.com/) to make your local bot accessible.
7. Copy your api key and paste it in `.env` as `POE_API_KEY`.
8. You will need to set `OPENAI_API_KEY` for generating embeddings and using LLM. Copy your OpenAI API key from [here](https://platform.openai.com/account/api-keys) and paste it in `.env` as `OPENAI_API_KEY`.
9. Now create your bot using the following code snippet.

```bash
# make sure that you have set OPENAI_API_KEY and POE_API_KEY in .env file
from embedchain.bots import PoeBot

poe_bot = PoeBot()

# add as many data sources as you want
poe_bot.add("https://en.wikipedia.org/wiki/Adam_D%27Angelo")
poe_bot.add("https://www.youtube.com/watch?v=pJQVAqmKua8")

# start the bot
# this start the poe bot server on port 8080 by default
poe_bot.start()
```

10. You can paste the above in a file called `your_script.py` and then simply do

```bash
python your_script.py
```

Now your bot will start running at port `8080` by default.

11. You can refer the [Supported Data formats](https://docs.embedchain.ai/advanced/data_types) section to refer the supported data types in embedchain.

12. Click `Run check` to make sure your machine can be reached.
13. Make sure your bot is private if that's what you want.
14. Click `Create bot` at the bottom to finally create the bot
15. Now your bot is created.

### 💬 How to use

- To ask the bot questions, just type your query in the Poe interface:
```text
<your-question-here>
```

- If you wish to add more data source to the bot, simply update your script and add as many `.add` as you like. You need to restart the server.
