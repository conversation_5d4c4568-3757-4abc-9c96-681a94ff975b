
import { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ChatProvider } from "@/contexts/ChatContext";
import Index from "./pages/Index";
import { aiConnection } from "@/utils/aiConnection";

const queryClient = new QueryClient();

const App = () => {
  useEffect(() => {
    // Check connection to Ollama/LM Studio on component mount
    aiConnection.checkConnection().then((connected) => {
      console.log("Ollama/LM Studio connection status:", connected ? "Connected" : "Not connected");
    });

    // Cleanup on unmount
    return () => {
      aiConnection.stopAutoReconnect();
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <ChatProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
            </Routes>
          </BrowserRouter>
        </ChatProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
