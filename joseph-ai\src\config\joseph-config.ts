
/**
 * Configuration file for <PERSON>
 * This file contains settings for the AI assistant, model preferences, and application behavior
 */

export const josephConfig = {
  // AI Model Configuration
  model: {
    // Default model to use (can be changed in UI if models endpoint is available)
    defaultModel: "deepseek-coder:7b",
    // Endpoint for the local LM Studio/Ollama instance
    endpoint: "http://localhost:1234",
    // Model parameters
    parameters: {
      temperature: 0.7,
      top_p: 0.95,
      max_tokens: 2000,
      presence_penalty: 0,
      frequency_penalty: 0,
    }
  },

  // System Prompt Configuration
  systemPrompt: {
    // Default system prompt for Joseph AI
    default: `You are <PERSON>, a helpful assistant specialized in software development, 
    project planning, and technical documentation. You help <PERSON>, a full-stack developer, 
    with coding tasks, project management, and technical advice. Be concise, useful, and focus 
    on providing practical solutions.`,
    
    // Enable loading custom system prompts from files
    enableCustomPrompts: true,
    
    // Path to custom prompts directory (relative to application root)
    customPromptsPath: "./prompts/"
  },
  
  // Application Settings
  app: {
    // Name of the application
    name: "<PERSON>",
    // Application theme (light/dark/system)
    theme: "system",
    // Message history storage type (localStorage/memory/none)
    storageType: "localStorage",
    // Maximum number of messages to store in history
    maxStoredMessages: 100,
    // Auto-reconnect to LM Studio if connection is lost
    autoReconnect: true,
    // Check interval for reconnection attempts (in milliseconds)
    reconnectInterval: 5000,
  },
  
  // Integration Settings
  integrations: {
    // Database configuration (for MySQL/PostgreSQL)
    database: {
      enabled: false,
      type: "mysql", // mysql, postgresql
      host: "localhost",
      port: 3306,
      user: "root",
      password: "",
      database: "joseph_ai"
    },
    
    // Supabase configuration (optional)
    supabase: {
      enabled: false,
      url: "",
      key: ""
    },
    
    // Vercel deployment settings
    vercel: {
      enabled: false,
      // Environment variables to be set in Vercel
      projectId: "",
      teamId: ""
    }
  },
  
  // Feature Toggles
  features: {
    // Enable file uploads for analysis
    fileUploads: false,
    // Enable command execution
    commandExecution: false,
    // Enable memory/vector storage
    vectorMemory: false,
    // Enable multi-agent support
    multiAgent: false,
    // Enable code execution in sandbox
    codeExecution: false
  }
};

export default josephConfig;
