import { useState, useEffect, useCallback } from 'react';
import { Message } from '@/contexts/ChatContext';
import { josephConfig } from '@/config/joseph-config';

export function useMessageStore() {
  const [messages, setMessages] = useState<Message[]>([]);
  
  // Load messages from localStorage if enabled
  useEffect(() => {
    if (josephConfig.app.storageType === 'localStorage') {
      const savedMessages = localStorage.getItem('joseph-ai-messages');
      
      if (savedMessages) {
        try {
          setMessages(JSON.parse(savedMessages));
        } catch (error) {
          console.error('Failed to parse saved messages:', error);
        }
      }
    }
  }, []);

  // Save messages to localStorage when they change
  useEffect(() => {
    if (josephConfig.app.storageType === 'localStorage' && messages.length > 0) {
      localStorage.setItem('joseph-ai-messages', JSON.stringify(messages.slice(-josephConfig.app.maxStoredMessages)));
    }
  }, [messages]);

  // Add a message
  const addMessage = useCallback((message: Message) => {
    setMessages(prevMessages => {
      // If message with same ID already exists, replace it
      if (prevMessages.some(m => m.id === message.id)) {
        return prevMessages.map(m => 
          m.id === message.id ? message : m
        );
      }
      // Otherwise add as new message
      return [...prevMessages, message];
    });
  }, []);

  // Update the last message
  const updateLastMessage = useCallback((content: string) => {
    setMessages(prevMessages => {
      const updatedMessages = [...prevMessages];
      const lastMessageIndex = updatedMessages.length - 1;
      
      if (lastMessageIndex >= 0 && updatedMessages[lastMessageIndex].role === 'assistant') {
        updatedMessages[lastMessageIndex].content = content;
      }
      
      return updatedMessages;
    });
  }, []);

  // Clear all messages
  const clearMessages = useCallback(() => {
    setMessages([]);
    
    if (josephConfig.app.storageType === 'localStorage') {
      localStorage.removeItem('joseph-ai-messages');
    }
  }, []);

  return {
    messages,
    addMessage,
    updateLastMessage,
    clearMessages
  };
}
