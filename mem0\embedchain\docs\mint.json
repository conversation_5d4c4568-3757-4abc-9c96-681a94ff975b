{"$schema": "https://mintlify.com/schema.json", "name": "Embedchain", "logo": {"dark": "/logo/dark-rt.svg", "light": "/logo/light-rt.svg", "href": "https://github.com/embedchain/embedchain"}, "favicon": "/favicon.png", "colors": {"primary": "#3B2FC9", "light": "#6673FF", "dark": "#3B2FC9", "background": {"dark": "#0f1117", "light": "#fff"}}, "modeToggle": {"default": "dark"}, "openapi": ["/rest-api.json"], "metadata": {"og:image": "/images/og.png", "twitter:site": "@embedchain"}, "tabs": [{"name": "Examples", "url": "examples"}, {"name": "API Reference", "url": "api-reference"}], "anchors": [{"name": "Talk to founders", "icon": "calendar", "url": "https://cal.com/taranjeetio/ec"}], "topbarLinks": [{"name": "GitHub", "url": "https://github.com/embedchain/embedchain"}], "topbarCtaButton": {"name": "Join our slack", "url": "https://embedchain.ai/slack"}, "primaryTab": {"name": "📘 Documentation"}, "navigation": [{"group": "Get Started", "pages": ["get-started/quickstart", "get-started/introduction", "get-started/faq", "get-started/full-stack", {"group": "🔗 Integrations", "pages": ["integration/langsmith", "integration/chainlit", "integration/streamlit-mistral", "integration/openlit", "integration/helicone"]}]}, {"group": "Use cases", "pages": ["use-cases/introduction", "use-cases/chatbots", "use-cases/question-answering", "use-cases/semantic-search"]}, {"group": "Components", "pages": ["components/introduction", {"group": "🗂️ Data sources", "pages": ["components/data-sources/overview", {"group": "Data types", "pages": ["components/data-sources/pdf-file", "components/data-sources/csv", "components/data-sources/json", "components/data-sources/text", "components/data-sources/directory", "components/data-sources/web-page", "components/data-sources/youtube-channel", "components/data-sources/youtube-video", "components/data-sources/docs-site", "components/data-sources/mdx", "components/data-sources/docx", "components/data-sources/notion", "components/data-sources/sitemap", "components/data-sources/xml", "components/data-sources/qna", "components/data-sources/openapi", "components/data-sources/gmail", "components/data-sources/github", "components/data-sources/postgres", "components/data-sources/mysql", "components/data-sources/slack", "components/data-sources/discord", "components/data-sources/discourse", "components/data-sources/substack", "components/data-sources/beehiiv", "components/data-sources/directory", "components/data-sources/dropbox", "components/data-sources/image", "components/data-sources/audio", "components/data-sources/custom"]}, "components/data-sources/data-type-handling"]}, {"group": "🗄️ Vector databases", "pages": ["components/vector-databases/chromadb", "components/vector-databases/elasticsearch", "components/vector-databases/pinecone", "components/vector-databases/opensearch", "components/vector-databases/qdrant", "components/vector-databases/weaviate", "components/vector-databases/zilliz"]}, "components/llms", "components/embedding-models", "components/evaluation"]}, {"group": "Deployment", "pages": ["get-started/deployment", "deployment/fly_io", "deployment/modal_com", "deployment/render_com", "deployment/railway", "deployment/streamlit_io", "deployment/gradio_app", "deployment/huggingface_spaces"]}, {"group": "Community", "pages": ["community/connect-with-us"]}, {"group": "Examples", "pages": ["examples/chat-with-PDF", "examples/notebooks-and-replits", {"group": "REST API Service", "pages": ["examples/rest-api/getting-started", "examples/rest-api/create", "examples/rest-api/get-all-apps", "examples/rest-api/add-data", "examples/rest-api/get-data", "examples/rest-api/query", "examples/rest-api/deploy", "examples/rest-api/delete", "examples/rest-api/check-status"]}, "examples/full_stack", "examples/openai-assistant", "examples/opensource-assistant", "examples/nextjs-assistant", "examples/slack-AI"]}, {"group": "Chatbots", "pages": ["examples/discord_bot", "examples/slack_bot", "examples/telegram_bot", "examples/whatsapp_bot", "examples/poe_bot"]}, {"group": "Showcase", "pages": ["examples/showcase"]}, {"group": "API Reference", "pages": ["api-reference/app/overview", {"group": "App methods", "pages": ["api-reference/app/add", "api-reference/app/query", "api-reference/app/chat", "api-reference/app/search", "api-reference/app/get", "api-reference/app/evaluate", "api-reference/app/deploy", "api-reference/app/reset", "api-reference/app/delete"]}, "api-reference/store/openai-assistant", "api-reference/store/ai-assistants", "api-reference/advanced/configuration"]}, {"group": "Contributing", "pages": ["contribution/guidelines", "contribution/dev", "contribution/docs", "contribution/python"]}, {"group": "Product", "pages": ["product/release-notes"]}], "footerSocials": {"website": "https://embedchain.ai", "github": "https://github.com/embedchain/embedchain", "slack": "https://embedchain.ai/slack", "discord": "https://discord.gg/6PzXDgEjG5", "twitter": "https://twitter.com/embedchain", "linkedin": "https://www.linkedin.com/company/embedchain"}, "isWhiteLabeled": true, "analytics": {"posthog": {"apiKey": "phc_PHQDA5KwztijnSojsxJ2c1DuJd52QCzJzT2xnSGvjN2", "apiHost": "https://app.embedchain.ai/ingest"}, "ga4": {"measurementId": "G-4QK7FJE6T3"}}, "feedback": {"suggestEdit": true, "raiseIssue": true, "thumbsRating": true}, "search": {"prompt": "✨ Search embedchain docs..."}, "api": {"baseUrl": "http://localhost:8080"}, "redirects": [{"source": "/changelog/command-line", "destination": "/get-started/introduction"}]}