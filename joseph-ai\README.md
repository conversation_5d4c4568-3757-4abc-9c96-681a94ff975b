
# Joseph AI

A full-stack AI assistant web app designed to interact with local AI models (via LM Studio/Ollama) and help manage coding workflows, project planning, tech documentation, and custom prompts.

## Features

- 🤖 Connect to local LLM models via LM Studio/Ollama
- 💬 Clean, modern chat interface
- 🔄 Auto-reconnect if server restarts
- 📱 Mobile and desktop responsive design
- ⚙️ Configurable system prompt and model parameters
- 💾 Local storage for chat history
- 🚀 Lightweight and fast

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- [LM Studio](https://lmstudio.ai/) or [Ollama](https://ollama.com/) running locally on port 1234
- Recommended: `deepseek-coder:7b` model loaded in your local AI server

### Installation

```bash
# Clone the repository
git clone https://github.com/YOUR_USERNAME/joseph-ai.git

# Navigate to the project directory
cd joseph-ai

# Install dependencies
npm install

# Start the development server
npm run dev
```

Visit `http://localhost:8080` to see the application running.

### Environment Setup

Make sure your LM Studio or Ollama instance is running and accessible at `http://localhost:1234`. The application will attempt to connect to this endpoint automatically.

## Project Structure

```
joseph-ai/
├── public/              # Static assets
├── src/
│   ├── api/             # API client functions
│   ├── components/      # UI components
│   ├── config/          # Configuration files
│   ├── contexts/        # React context providers
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility libraries
│   ├── pages/           # Page components
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── App.tsx          # Main application component
│   └── main.tsx         # Application entry point
```

## Development Workflow

### Local Development

1. Start your LM Studio or Ollama server on port 1234
2. Run `npm run dev` to start the Vite development server
3. Open `http://localhost:8080` in your browser

### Building for Production

```bash
npm run build
```

The build output will be in the `dist` directory, ready for deployment.

## Connecting with Different AI Models

Joseph AI is designed to work with any LM Studio or Ollama model. Configure your preferred model in the `config/joseph-config.ts` file.

## Architecture

Joseph AI uses a clean architecture with separate concerns:

- `contexts/ChatContext.tsx`: Main application state and logic
- `hooks/`: Custom React hooks for state management and API communication
- `components/`: Reusable UI components
- `utils/aiConnection.ts`: Core communication layer with the AI server

## License

This project is licensed under the MIT License - see the LICENSE file for details.
