
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "@/components/ui/use-toast";
import { useMessageStore } from "@/hooks/useMessageStore";
import { useSystemPrompt } from "@/hooks/useSystemPrompt";
import { useConnectionStatus } from "@/hooks/useConnectionStatus";
import { sendMessageStream } from "@/api/aiClient";
import { AIConnectionStatus } from "@/utils/aiConnection";
import { josephConfig } from "@/config/joseph-config";

// Define our extended Message type that includes id and timestamp
export type Message = {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: number;
};

// Define the type for messages to be sent to the API (without id and timestamp)
export type ApiMessage = {
  role: "user" | "assistant" | "system";
  content: string;
};

interface ChatContextType {
  messages: Message[];
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  isLoading: boolean;
  connectionStatus: AIConnectionStatus;
  currentModel: string;
  setCurrentModel: (model: string) => void;
  apiKey: string;
  setApiKey: (key: string) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const ChatProvider = ({ children }: { children: ReactNode }) => {
  const { messages, addMessage, clearMessages } = useMessageStore();
  const { systemPrompt } = useSystemPrompt();
  const { connectionStatus } = useConnectionStatus();
  const [isLoading, setIsLoading] = useState(false);
  const [currentModel, setCurrentModel] = useState(josephConfig.model.defaultModel);
  const [apiKey, setApiKey] = useState("");

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      role: "user" as const,
      content,
      timestamp: Date.now(),
    };
    addMessage(userMessage);

    // Prepare messages for API
    const messagesToSend: ApiMessage[] = [
      { role: "system" as const, content: systemPrompt },
      ...messages
        .filter((msg) => msg.role !== "system")
        .map((msg) => ({
          role: msg.role,
          content: msg.content,
        })),
      { role: "user" as const, content },
    ];

    // Add assistant message placeholder
    const assistantMessageId = (Date.now() + 1).toString();
    addMessage({
      id: assistantMessageId,
      role: "assistant",
      content: "",
      timestamp: Date.now(),
    });

    // Stream the response
    setIsLoading(true);
    let fullResponse = "";

    try {
      await sendMessageStream(
        messagesToSend,
        (text, done) => {
          fullResponse += text;
          addMessage({
            id: assistantMessageId,
            role: "assistant",
            content: fullResponse,
            timestamp: Date.now(),
          });
          if (done) {
            setIsLoading(false);
          }
        },
        () => {
          setIsLoading(false);
        },
        (error) => {
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          });
          setIsLoading(false);
        }
      );
    } catch (error) {
      console.error("Failed to send message:", error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  return (
    <ChatContext.Provider
      value={{
        messages,
        sendMessage,
        clearMessages,
        isLoading,
        connectionStatus,
        currentModel,
        setCurrentModel,
        apiKey,
        setApiKey
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
