
# Developer Setup Guide

This guide provides detailed instructions for setting up and working with the Joseph AI project.

## Prerequisites

- Node.js (v18+) and npm
- LM Studio or Ollama installed and running locally
- Git for version control
- VS Code (recommended) with extensions:
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense

## Initial Setup

1. **Clone the repository**

```bash
git clone https://github.com/YOUR_USERNAME/joseph-ai.git
cd joseph-ai
```

2. **Install dependencies**

```bash
npm install
```

3. **Set up environment**

Copy the example environment file to create your local environment:

```bash
cp .env.example .env.local
```

Edit `.env.local` if you need to customize any settings.

4. **Start the local LLM server**

- Option 1: Start LM Studio and run a model on port 1234
- Option 2: Start Ollama with a command like:
  ```bash
  ollama run deepseek-coder:7b
  ```

5. **Start the development server**

```bash
npm run dev
```

Visit `http://localhost:8080` in your browser.

## Development Workflow

### Code Conventions

- Follow the TypeScript guidelines defined in `tsconfig.json`
- Use Tailwind CSS for styling
- Use the shadcn/ui component library whenever possible
- Keep components small and focused
- Follow React best practices with hooks

### Project Structure

- Put new components in `src/components/`
- Add new hooks in `src/hooks/`
- Update configuration in `src/config/joseph-config.ts`
- Add utility functions in `src/utils/`
- Add API functions in `src/api/`

### Testing Your Changes

1. Run the development server with `npm run dev`
2. Test the UI functionality in your browser
3. Check console logs for any errors
4. Verify connection status with your local LLM

### Building for Production

```bash
npm run build
```

The output will be in the `dist` directory.

### Integration Points

- **Local LLM**: The app connects to LM Studio or Ollama running on port 1234
- **GitHub**: Push code changes and PR workflow
- **VS Code**: Local development environment
- **Lovable**: AI-assisted development and deployment
- **Vercel** (optional): For deployment

## Troubleshooting

### Common Issues

1. **Connection Error**:
   - Ensure LM Studio or Ollama is running on port 1234
   - Check network settings and firewalls
   - Verify the model is loaded correctly

2. **Build Errors**:
   - Run `npm install` to ensure all dependencies are updated
   - Check for TypeScript errors in your code
   - Verify imports and paths are correct

3. **UI Issues**:
   - Clear browser cache
   - Check for console errors
   - Verify Tailwind CSS classes are working

### Getting Help

If you need assistance, check:
- Project documentation in the `docs` folder
- Issue tracker on GitHub
- Project wiki (if available)
