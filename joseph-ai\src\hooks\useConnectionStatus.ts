
import { useState, useEffect } from 'react';
import { getConnectionStatus, onConnectionStatusChange } from '@/api/aiClient';
import { AIConnectionStatus } from '@/utils/aiConnection';
import { useToast } from "@/hooks/use-toast";

export function useConnectionStatus() {
  const [connectionStatus, setConnectionStatus] = useState<AIConnectionStatus>(getConnectionStatus());
  const { toast } = useToast();
  
  // Subscribe to connection status changes
  useEffect(() => {
    const unsubscribe = onConnectionStatusChange((status) => {
      setConnectionStatus(status as AIConnectionStatus);
      
      if (status === 'connected') {
        toast({
          title: "Connected",
          description: "Successfully connected to LM Studio/Ollama",
        });
      } else if (status === 'error') {
        toast({
          title: "Connection Error",
          description: "Failed to connect to LM Studio/Ollama",
          variant: "destructive"
        });
      }
    });
    
    return unsubscribe;
  }, [toast]);

  return {
    connectionStatus
  };
}
