import { josephConfig } from "@/config/joseph-config";
import { ApiMessage } from "@/contexts/ChatContext";

// Types for API responses and requests
export type Message = {
  role: "user" | "assistant" | "system";
  content: string;
};

export type ChatCompletionRequest = {
  model: string;
  messages: ApiMessage[];
  stream: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
};

export type AIConnectionStatus = "connected" | "disconnected" | "connecting" | "error";

// Class to handle connection to local LM Studio/Ollama
export class AIConnection {
  private endpoint: string;
  private currentStatus: AIConnectionStatus = "disconnected";
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private statusListeners: ((status: AIConnectionStatus) => void)[] = [];
  private apiKey: string = "";
  private currentModel: string = josephConfig.model.defaultModel;

  constructor(endpoint = josephConfig.model.endpoint) {
    this.endpoint = endpoint;
  }

  // Set API key for authentication (if needed)
  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  // Set the current model to use
  public setModel(model: string): void {
    this.currentModel = model;
  }

  // Get the current selected model
  public getModel(): string {
    return this.currentModel;
  }

  // Check if LM Studio/Ollama is running
  public async checkConnection(): Promise<boolean> {
    try {
      this.setStatus("connecting");
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Add API key if present
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }
      
      const response = await fetch(`${this.endpoint}/v1/models`, {
        method: 'GET',
        headers
      });
      
      const success = response.ok;
      this.setStatus(success ? "connected" : "error");
      return success;
    } catch (error) {
      console.error("Failed to connect to LM Studio/Ollama:", error);
      this.setStatus("error");
      return false;
    }
  }

  // Start auto-reconnect if enabled in config
  public startAutoReconnect(): void {
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
    }

    if (josephConfig.app.autoReconnect) {
      this.reconnectTimer = setInterval(async () => {
        if (this.currentStatus !== "connected") {
          await this.checkConnection();
        }
      }, josephConfig.app.reconnectInterval);
    }
  }

  // Stop auto-reconnect
  public stopAutoReconnect(): void {
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // Send a chat completion request to LM Studio/Ollama
  public async sendChatCompletion(messages: ApiMessage[], model?: string): Promise<ReadableStream<Uint8Array> | null> {
    try {
      const { temperature, max_tokens, top_p, presence_penalty, frequency_penalty } = josephConfig.model.parameters;
      
      const payload: ChatCompletionRequest = {
        model: model || this.currentModel,
        messages,
        stream: true,
        temperature,
        max_tokens,
        top_p,
        presence_penalty,
        frequency_penalty
      };

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Add API key if present
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      const response = await fetch(`${this.endpoint}/v1/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error("No response body received");
      }

      return response.body;
    } catch (error) {
      console.error("Failed to send chat completion:", error);
      this.setStatus("error");
      return null;
    }
  }

  // Get available models from LM Studio/Ollama
  public async getAvailableModels(): Promise<string[]> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Add API key if present
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }
      
      const response = await fetch(`${this.endpoint}/v1/models`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.data.map((model: any) => model.id);
    } catch (error) {
      console.error("Failed to get available models:", error);
      return [];
    }
  }

  // Subscribe to status changes
  public onStatusChange(callback: (status: AIConnectionStatus) => void): () => void {
    this.statusListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.statusListeners = this.statusListeners.filter(listener => listener !== callback);
    };
  }

  // Get current connection status
  public getStatus(): AIConnectionStatus {
    return this.currentStatus;
  }

  // Set connection status and notify listeners
  private setStatus(status: AIConnectionStatus): void {
    this.currentStatus = status;
    this.statusListeners.forEach(listener => listener(status));
  }
}

// Create singleton instance
export const aiConnection = new AIConnection();

// Initialize connection check on import
aiConnection.checkConnection().then(() => {
  aiConnection.startAutoReconnect();
});
