---
title: 'Fly.io'
description: 'Deploy your RAG application to fly.io platform'
---

Embedchain has a nice and simple abstraction on top of the [Fly.io](https://fly.io/) tools to let developers deploy RAG application to fly.io platform seamlessly. 

Follow the instructions given below to deploy your first application quickly:


## Step-1: Install flyctl command line

<CodeGroup>
```bash OSX
brew install flyctl
```

```bash Linux
curl -L https://fly.io/install.sh | sh
```

```bash Windows
pwsh -Command "iwr https://fly.io/install.ps1 -useb | iex"
```
</CodeGroup>

Once you have installed the fly.io cli tool, signup/login to their platform using the following command:

<CodeGroup>
```bash Sign up
fly auth signup
```

```bash Sign in
fly auth login
```
</CodeGroup>

In case you run into issues, refer to official [fly.io docs](https://fly.io/docs/hands-on/install-flyctl/).

## Step-2: Create RAG app

We provide a command line utility called `ec` in embedchain that inherits the template for `fly.io` platform and help you deploy the app. Follow the instructions to create a fly.io app using the template provided:

```bash Install embedchain
pip install embedchain
```

```bash Create application
mkdir my-rag-app
ec create --template=fly.io
```

This will generate a directory structure like this:

```bash
├── Dockerfile
├── app.py
├── fly.toml
├── .env
├── .env.example
├── embedchain.json
└── requirements.txt
```

Feel free to edit the files as required.
- `Dockerfile`: Defines the steps to setup the application
- `app.py`: Contains API app code
- `fly.toml`: fly.io config file
- `.env`: Contains environment variables for production
- `.env.example`: Contains dummy environment variables (can ignore this file)
- `embedchain.json`: Contains embedchain specific configuration for deployment (you don't need to configure this)
- `requirements.txt`: Contains python dependencies for your application

## Step-3: Test app locally

You can run the app locally by simply doing:

```bash Run locally
pip install -r requirements.txt
ec dev
```

## Step-4: Deploy to fly.io

You can deploy to fly.io using the following command:
```bash Deploy app
ec deploy
```

Once this step finished, it will provide you with the deployment endpoint where you can access the app live. It will look something like this (Swagger docs):

You can also check the logs, monitor app status etc on their dashboard by running command `fly dashboard`.

<img src="/images/fly_io.png" />

## Seeking help?

If you run into issues with deployment, please feel free to reach out to us via any of the following methods:

<Snippet file="get-help.mdx" />
