app:
  config:
    id: 'my-app'
    log_level: 'WARNING'
    collect_metrics: true
    collection_name: 'my-app'

llm:
  provider: openai
  config:
    model: 'gpt-4o-mini'
    temperature: 0.5
    max_tokens: 1000
    top_p: 1
    stream: false

vectordb:
  provider: opensearch
  config:
    opensearch_url: 'https://localhost:9200'
    http_auth:
      - admin
      - admin
    vector_dimension: 1536
    collection_name: 'my-app'
    use_ssl: false
    verify_certs: false

embedder:
  provider: openai
  config:
    model: 'text-embedding-ada-002'
    deployment_name: 'my-app'
